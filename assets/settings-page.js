/**
 * Git Manager Settings Page JavaScript
 */
jQuery(document).ready(function($) {
    'use strict';
    
    const SettingsPage = {
        
        init: function() {
            this.bindEvents();
        },
        
        bindEvents: function() {
            $('#test-git-path').on('click', this.handleTestGitPath);
            $('#test-connection').on('click', this.handleTestConnection);
        },

        handleTestGitPath: function() {
            var button = $(this);
            var originalText = button.text();
            button.prop('disabled', true).text('Testing...');

            var gitPath = $('input[name="git_path"]').val();

            $.post(ajaxurl, {
                    action: 'git_setup_check',
                    nonce: wpGitManager.nonce,
                    check_type: 'git_path',
                    git_path: gitPath
                })
                .done(function(response) {
                    if (response.success) {
                        button.after('<span class="test-result success" style="margin-left: 10px; color: #00a32a;">✓ ' + response.data.version + '</span>');
                    } else {
                        button.after('<span class="test-result error" style="margin-left: 10px; color: #d63638;">✗ ' + response.data + '</span>');
                    }
                    setTimeout(function() {
                        $('.test-result').fadeOut(function() {
                            $(this).remove();
                        });
                    }, 5000);
                })
                .always(function() {
                    button.prop('disabled', false).text(originalText);
                });
        },

        handleTestConnection: function() {
            var button = $(this);
            button.prop('disabled', true).text('Testing...');

            var gitPath = $('input[name="git_path"]').val();

            $.post(ajaxurl, {
                    action: 'git_setup_check',
                    nonce: wpGitManager.nonce,
                    check_type: 'git_path',
                    git_path: gitPath
                })
                .done(function(response) {
                    if (response.success) {
                        $('#action-output').html('✓ Git connection successful!\nVersion: ' + response.data.version);
                    } else {
                        $('#action-output').html('✗ Git connection failed: ' + response.data);
                    }
                })
                .always(function() {
                    button.prop('disabled', false).text('Test Git Connection');
                });
        }
    };

    // Initialize SettingsPage
    SettingsPage.init();
});
