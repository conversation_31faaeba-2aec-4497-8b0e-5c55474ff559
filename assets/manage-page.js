/**
 * Git Manager Manage Page JavaScript
 */
jQuery(document).ready(function($) {
    'use strict';
    
    const ManagePage = {
        
        init: function() {
            this.bindEvents();
        },
        
        bindEvents: function() {
            // File management
            $('#select-all-files').on('change', this.handleSelectAll);
            $(document).on('click', '.git-stage-file', this.handleStageFile);
            $(document).on('click', '.git-unstage-file', this.handleUnstageFile);
            $(document).on('click', '.git-view-diff', this.handleViewDiff);
            $('#stage-all').on('click', this.handleStageAll);
            $('#unstage-all').on('click', this.handleUnstageAll);
            $('#refresh-status').on('click', this.handleRefreshStatus);

            // Commit operations
            $('#create-commit').on('click', this.handleCreateCommit);

            // Branch management
            $('#create-branch').on('click', this.handleCreateBranch);
            $('#load-branches').on('click', this.handleLoadBranches);

            // Remote management
            $('#add-remote').on('click', this.handleAddRemote);
            $('#load-remotes').on('click', this.handleLoadRemotes);

            // History
            $('#load-history').on('click', this.handleLoadHistory);

            // Advanced operations
            $('#view-git-log').on('click', this.handleViewLog);
            $('#view-git-status').on('click', this.handleViewStatus);
            $('#view-git-config').on('click', this.handleViewConfig);
            $('#git-gc').on('click', this.handleGarbageCollection);
            $('#git-fsck').on('click', this.handleFileSystemCheck);
            $('#reset-hard').on('click', this.handleResetHard);
            $('#clean-untracked').on('click', this.handleCleanUntracked);

            // Push/Pull buttons
            $('.git-push-btn').on('click', this.handlePush);
            $('.git-pull-btn').on('click', this.handlePull);
            $('.git-commit-btn').on('click', this.handleQuickCommit);
        },

        handleSelectAll: function() {
            const isChecked = $(this).is(':checked');
            $('input[name="selected_files[]"]').prop('checked', isChecked);
        },

        handleStageFile: function(e) {
            e.preventDefault();
            const file = $(this).data('file');
            ManagePage.stageFile(file);
        },

        handleUnstageFile: function(e) {
            e.preventDefault();
            const file = $(this).data('file');
            ManagePage.unstageFile(file);
        },

        handleViewDiff: function(e) {
            e.preventDefault();
            const file = $(this).data('file');
            ManagePage.viewDiff(file);
        },

        handleStageAll: function(e) {
            e.preventDefault();
            ManagePage.stageAll();
        },

        handleUnstageAll: function(e) {
            e.preventDefault();
            ManagePage.unstageAll();
        },

        handleRefreshStatus: function(e) {
            e.preventDefault();
            location.reload();
        },

        handleCreateCommit: function(e) {
            e.preventDefault();
            const message = $('#commit-message').val();
            const autoStage = $('#auto-stage').is(':checked');

            if (!message.trim()) {
                alert('Please enter a commit message.');
                return;
            }

            ManagePage.createCommit(message, autoStage);
        },

        handleCreateBranch: function(e) {
            e.preventDefault();
            const branchName = $('#new-branch-name').val();

            if (!branchName.trim()) {
                alert('Please enter a branch name.');
                return;
            }

            ManagePage.createBranch(branchName);
        },

        handleLoadBranches: function(e) {
            e.preventDefault();
            ManagePage.loadBranches();
        },

        handleAddRemote: function(e) {
            e.preventDefault();
            const name = $('#remote-name').val();
            const url = $('#remote-url').val();

            if (!name.trim() || !url.trim()) {
                alert('Please enter both remote name and URL.');
                return;
            }

            ManagePage.addRemote(name, url);
        },

        handleLoadRemotes: function(e) {
            e.preventDefault();
            ManagePage.loadRemotes();
        },

        handleLoadHistory: function(e) {
            e.preventDefault();
            const limit = $('#history-limit').val();
            ManagePage.loadHistory(limit);
        },

        handleViewLog: function(e) {
            e.preventDefault();
            ManagePage.executeGitCommand('log --oneline -10');
        },

        handleViewStatus: function(e) {
            e.preventDefault();
            ManagePage.executeGitCommand('status');
        },

        handleViewConfig: function(e) {
            e.preventDefault();
            ManagePage.executeGitCommand('config --list');
        },

        handleGarbageCollection: function(e) {
            e.preventDefault();
            if (confirm('Run garbage collection? This will optimize the repository.')) {
                ManagePage.executeGitCommand('gc');
            }
        },

        handleFileSystemCheck: function(e) {
            e.preventDefault();
            if (confirm('Run file system check? This will verify repository integrity.')) {
                ManagePage.executeGitCommand('fsck');
            }
        },

        handleResetHard: function(e) {
            e.preventDefault();
            if (confirm('WARNING: This will permanently discard all uncommitted changes. Are you sure?')) {
                ManagePage.executeGitCommand('reset --hard HEAD');
            }
        },

        handleCleanUntracked: function(e) {
            e.preventDefault();
            if (confirm('WARNING: This will permanently delete all untracked files. Are you sure?')) {
                ManagePage.executeGitCommand('clean -fd');
            }
        },

        handlePush: function(e) {
            e.preventDefault();
            ManagePage.showLoading('Pushing to remote...');

            $.post(ajaxurl, {
                action: 'git_push',
                nonce: wpGitManager.nonce
            }, function(response) {
                ManagePage.hideLoading();
                if (response.success) {
                    ManagePage.showMessage('Push successful', 'success');
                } else {
                    ManagePage.showMessage('Push failed: ' + response.data, 'error');
                }
            }).fail(function(xhr, status, error) {
                ManagePage.hideLoading();
                console.error('Push AJAX Error:', xhr.responseText);
                ManagePage.showMessage('Push AJAX Error: ' + error + ' (Status: ' + xhr.status + ')', 'error');
            });
        },

        handlePull: function(e) {
            e.preventDefault();
            ManagePage.showLoading('Pulling from remote...');

            $.post(ajaxurl, {
                action: 'git_pull',
                nonce: wpGitManager.nonce
            }, function(response) {
                ManagePage.hideLoading();
                if (response.success) {
                    ManagePage.showMessage('Pull successful', 'success');
                    location.reload();
                } else {
                    ManagePage.showMessage('Pull failed: ' + response.data, 'error');
                }
            }).fail(function(xhr, status, error) {
                ManagePage.hideLoading();
                console.error('Pull AJAX Error:', xhr.responseText);
                ManagePage.showMessage('Pull AJAX Error: ' + error + ' (Status: ' + xhr.status + ')', 'error');
            });
        },

        handleQuickCommit: function(e) {
            e.preventDefault();
            const message = prompt('Enter commit message:');
            if (message) {
                ManagePage.createCommit(message, true);
            }
        },

        // API functions
        stageFile: function(file) {
            this.showLoading('Staging file...');

            $.post(ajaxurl, {
                action: 'git_stage_file',
                file: file,
                nonce: wpGitManager.nonce
            }, function(response) {
                ManagePage.hideLoading();
                if (response.success) {
                    ManagePage.showMessage('File staged successfully', 'success');
                    location.reload();
                } else {
                    ManagePage.showMessage('Error staging file: ' + response.data, 'error');
                }
            }).fail(function(xhr, status, error) {
                ManagePage.hideLoading();
                console.error('Stage file AJAX Error:', xhr.responseText);
                ManagePage.showMessage('AJAX Error: ' + error + ' (Status: ' + xhr.status + ')', 'error');
            });
        },

        unstageFile: function(file) {
            this.showLoading('Unstaging file...');

            $.post(ajaxurl, {
                action: 'git_unstage_file',
                file: file,
                nonce: wpGitManager.nonce
            }, function(response) {
                ManagePage.hideLoading();
                if (response.success) {
                    ManagePage.showMessage('File unstaged successfully', 'success');
                    location.reload();
                } else {
                    ManagePage.showMessage('Error unstaging file: ' + response.data, 'error');
                }
            }).fail(function(xhr, status, error) {
                ManagePage.hideLoading();
                console.error('Unstage file AJAX Error:', xhr.responseText);
                ManagePage.showMessage('AJAX Error: ' + error + ' (Status: ' + xhr.status + ')', 'error');
            });
        },

        stageAll: function() {
            this.showLoading('Staging all files...');

            $.post(ajaxurl, {
                action: 'git_stage_all',
                nonce: wpGitManager.nonce
            }, function(response) {
                ManagePage.hideLoading();
                if (response.success) {
                    ManagePage.showMessage('All files staged successfully', 'success');
                    location.reload();
                } else {
                    ManagePage.showMessage('Error staging files: ' + response.data, 'error');
                }
            }).fail(function(xhr, status, error) {
                ManagePage.hideLoading();
                console.error('Stage all AJAX Error:', xhr.responseText);
                ManagePage.showMessage('AJAX Error: ' + error + ' (Status: ' + xhr.status + ')', 'error');
            });
        },

        unstageAll: function() {
            this.showLoading('Unstaging all files...');

            $.post(ajaxurl, {
                action: 'git_unstage_all',
                nonce: wpGitManager.nonce
            }, function(response) {
                ManagePage.hideLoading();
                if (response.success) {
                    ManagePage.showMessage('All files unstaged successfully', 'success');
                    location.reload();
                } else {
                    ManagePage.showMessage('Error unstaging files: ' + response.data, 'error');
                }
            }).fail(function(xhr, status, error) {
                ManagePage.hideLoading();
                console.error('Unstage all AJAX Error:', xhr.responseText);
                ManagePage.showMessage('AJAX Error: ' + error + ' (Status: ' + xhr.status + ')', 'error');
            });
        },

        viewDiff: function(file) {
            this.showLoading('Loading diff...');

            $.post(ajaxurl, {
                action: 'git_file_diff',
                file: file,
                nonce: wpGitManager.nonce
            }, function(response) {
                ManagePage.hideLoading();
                if (response.success) {
                    ManagePage.showDiff(file, response.data.diff);
                } else {
                    ManagePage.showMessage('Error loading diff: ' + response.data, 'error');
                }
            }).fail(function(xhr, status, error) {
                ManagePage.hideLoading();
                console.error('View diff AJAX Error:', xhr.responseText);
                ManagePage.showMessage('AJAX Error: ' + error + ' (Status: ' + xhr.status + ')', 'error');
            });
        },

        createCommit: function(message, autoStage) {
            this.showLoading('Creating commit...');

            $.post(ajaxurl, {
                action: 'git_commit',
                message: message,
                auto_stage: autoStage,
                nonce: wpGitManager.nonce
            }, function(response) {
                ManagePage.hideLoading();
                if (response.success) {
                    ManagePage.showMessage('Commit created successfully', 'success');
                    $('#commit-message').val('');
                    location.reload();
                } else {
                    ManagePage.showMessage('Error creating commit: ' + response.data, 'error');
                }
            }).fail(function(xhr, status, error) {
                ManagePage.hideLoading();
                console.error('Create commit AJAX Error:', xhr.responseText);
                ManagePage.showMessage('AJAX Error: ' + error + ' (Status: ' + xhr.status + ')', 'error');
            });
        },

        createBranch: function(branchName) {
            this.showLoading('Creating branch...');

            $.post(ajaxurl, {
                action: 'git_create_branch',
                branch_name: branchName,
                nonce: wpGitManager.nonce
            }, function(response) {
                ManagePage.hideLoading();
                if (response.success) {
                    ManagePage.showMessage('Branch created successfully', 'success');
                    $('#new-branch-name').val('');
                    ManagePage.loadBranches();
                } else {
                    ManagePage.showMessage('Error creating branch: ' + response.data, 'error');
                }
            }).fail(function(xhr, status, error) {
                ManagePage.hideLoading();
                console.error('Create branch AJAX Error:', xhr.responseText);
                ManagePage.showMessage('AJAX Error: ' + error + ' (Status: ' + xhr.status + ')', 'error');
            });
        },

        loadBranches: function() {
            this.showLoading('Loading branches...');

            $.post(ajaxurl, {
                action: 'git_get_branches',
                nonce: wpGitManager.nonce
            }, function(response) {
                ManagePage.hideLoading();
                if (response.success) {
                    ManagePage.displayBranches(response.data);
                } else {
                    ManagePage.showMessage('Error loading branches: ' + response.data, 'error');
                }
            }).fail(function(xhr, status, error) {
                ManagePage.hideLoading();
                console.error('Load branches AJAX Error:', xhr.responseText);
                ManagePage.showMessage('AJAX Error: ' + error + ' (Status: ' + xhr.status + ')', 'error');
            });
        },

        addRemote: function(name, url) {
            this.showLoading('Adding remote...');

            $.post(ajaxurl, {
                action: 'git_add_remote',
                remote_name: name,
                remote_url: url,
                nonce: wpGitManager.nonce
            }, function(response) {
                ManagePage.hideLoading();
                if (response.success) {
                    ManagePage.showMessage('Remote added successfully', 'success');
                    $('#remote-name').val('');
                    $('#remote-url').val('');
                    ManagePage.loadRemotes();
                } else {
                    ManagePage.showMessage('Error adding remote: ' + response.data, 'error');
                }
            }).fail(function(xhr, status, error) {
                ManagePage.hideLoading();
                console.error('Add remote AJAX Error:', xhr.responseText);
                ManagePage.showMessage('AJAX Error: ' + error + ' (Status: ' + xhr.status + ')', 'error');
            });
        },

        loadRemotes: function() {
            this.showLoading('Loading remotes...');

            $.post(ajaxurl, {
                action: 'git_get_remotes',
                nonce: wpGitManager.nonce
            }, function(response) {
                ManagePage.hideLoading();
                if (response.success) {
                    ManagePage.displayRemotes(response.data);
                } else {
                    ManagePage.showMessage('Error loading remotes: ' + response.data, 'error');
                }
            }).fail(function(xhr, status, error) {
                ManagePage.hideLoading();
                console.error('Load remotes AJAX Error:', xhr.responseText);
                ManagePage.showMessage('AJAX Error: ' + error + ' (Status: ' + xhr.status + ')', 'error');
            });
        },

        loadHistory: function(limit) {
            this.showLoading('Loading commit history...');

            $.post(ajaxurl, {
                action: 'git_commit_history',
                limit: limit,
                nonce: wpGitManager.nonce
            }, function(response) {
                ManagePage.hideLoading();
                if (response.success) {
                    ManagePage.displayHistory(response.data.commits);
                } else {
                    ManagePage.showMessage('Error loading history: ' + response.data, 'error');
                }
            }).fail(function(xhr, status, error) {
                ManagePage.hideLoading();
                console.error('Load history AJAX Error:', xhr.responseText);
                ManagePage.showMessage('AJAX Error: ' + error + ' (Status: ' + xhr.status + ')', 'error');
            });
        },

        executeGitCommand: function(command) {
            this.showLoading('Executing command...');

            $.post(ajaxurl, {
                action: 'git_command',
                git_command: command,
                nonce: wpGitManager.nonce
            }, function(response) {
                ManagePage.hideLoading();
                if (response.success) {
                    ManagePage.showOutput(command, response.data.message);
                } else {
                    ManagePage.showMessage('Error executing command: ' + response.data, 'error');
                }
            }).fail(function(xhr, status, error) {
                ManagePage.hideLoading();
                console.error('AJAX Error:', xhr.responseText);
                ManagePage.showMessage('AJAX Error: ' + error + ' (Status: ' + xhr.status + ')', 'error');
            });
        },

        // UI helper functions
        showLoading: function(message) {
            $('#git-output').html('<div class="notice notice-info"><p>' + message + '</p></div>');
        },

        hideLoading: function() {
            $('#git-output').empty();
        },

        showMessage: function(message, type) {
            const className = type === 'success' ? 'notice-success' : 'notice-error';
            $('#git-output').html('<div class="notice ' + className + ' is-dismissible"><p>' + message + '</p></div>');
        },

        showOutput: function(command, output) {
            const html = '<div class="git-command-output">' +
                '<h4>Command: git ' + command + '</h4>' +
                '<pre>' + output + '</pre>' +
                '</div>';
            $('#git-output').html(html);
        },

        showDiff: function(file, diff) {
            const html = '<div class="git-diff-output">' +
                '<h4>Diff for: ' + file + '</h4>' +
                '<pre class="diff-content">' + diff + '</pre>' +
                '</div>';
            $('#git-output').html(html);
        },

        displayBranches: function(data) {
            let html = '<table class="wp-list-table widefat fixed striped">';
            html += '<thead><tr><th>Branch</th><th>Current</th><th>Actions</th></tr></thead><tbody>';

            if (data.branches && data.branches.length > 0) {
                data.branches.forEach(function(branch) {
                    const isCurrent = branch === data.current_branch;
                    html += '<tr>';
                    html += '<td><code>' + branch + '</code></td>';
                    html += '<td>' + (isCurrent ? '<strong>✓ Current</strong>' : '') + '</td>';
                    html += '<td>';
                    if (!isCurrent) {
                        html += '<button class="button button-small git-switch-branch" data-branch="' + branch + '">Switch</button>';
                    }
                    html += '</td>';
                    html += '</tr>';
                });
            } else {
                html += '<tr><td colspan="3">No branches found</td></tr>';
            }

            html += '</tbody></table>';
            $('#branch-list-content').html(html);

            // Bind switch branch events
            $('.git-switch-branch').on('click', function(e) {
                e.preventDefault();
                const branch = $(this).data('branch');
                if (confirm('Switch to branch: ' + branch + '?')) {
                    ManagePage.switchBranch(branch);
                }
            });
        },

        displayRemotes: function(remotes) {
            let html = '<table class="wp-list-table widefat fixed striped">';
            html += '<thead><tr><th>Name</th><th>URL</th><th>Actions</th></tr></thead><tbody>';

            if (remotes && remotes.length > 0) {
                remotes.forEach(function(remote) {
                    html += '<tr>';
                    html += '<td><strong>' + remote.name + '</strong></td>';
                    html += '<td><code>' + remote.url + '</code></td>';
                    html += '<td><button class="button button-small button-link-delete">Remove</button></td>';
                    html += '</tr>';
                });
            } else {
                html += '<tr><td colspan="3">No remotes configured</td></tr>';
            }

            html += '</tbody></table>';
            $('#remote-list-content').html(html);
        },

        displayHistory: function(commits) {
            let html = '';

            if (commits && commits.length > 0) {
                console.log('Commits: ', commits);

                // Create table structure
                html += '<table class="wp-list-table widefat fixed striped">';
                html += '<thead>';
                html += '<tr>';
                html += '<th scope="col" class="manage-column column-hash" style="width: 100px;">Hash</th>';
                html += '<th scope="col" class="manage-column column-message">Message</th>';
                html += '<th scope="col" class="manage-column column-author" style="width: 150px;">Author</th>';
                html += '<th scope="col" class="manage-column column-date" style="width: 120px;">Date</th>';
                html += '<th scope="col" class="manage-column column-relative" style="width: 100px;">Relative</th>';
                html += '</tr>';
                html += '</thead>';
                html += '<tbody>';

                commits.forEach(function(commit) {
                    html += '<tr>';

                    // Handle both string format (from git log --oneline) and object format
                    if (typeof commit === 'string') {
                        // Parse string format like "f9a7a9d Initial commit"
                        const spaceIndex = commit.indexOf(' ');
                        const hash = spaceIndex > 0 ? commit.substring(0, spaceIndex) : commit.substring(0, 8);
                        const message = spaceIndex > 0 ? commit.substring(spaceIndex + 1) : 'No message';

                        html += '<td><code>' + hash + '</code></td>';
                        html += '<td>' + message + '</td>';
                        html += '<td>-</td>';
                        html += '<td>-</td>';
                        html += '<td>-</td>';
                    } else {
                        // Handle object format with hash, message, author, date properties
                        const shortHash = commit.hash.substring(0, 8);
                        const authorDisplay = commit.author + (commit.email ? ' <' + commit.email + '>' : '');

                        html += '<td><code title="' + commit.hash + '">' + shortHash + '</code></td>';
                        html += '<td>' + commit.message + '</td>';
                        html += '<td title="' + authorDisplay + '">' + commit.author + '</td>';
                        html += '<td>' + commit.date + '</td>';
                        html += '<td>' + (commit.relative_date || '-') + '</td>';
                    }

                    html += '</tr>';
                });

                html += '</tbody>';
                html += '</table>';
            } else {
                html = '<p>No commits found in repository.</p>';
            }

            $('#commit-history').html(html);
        },

        switchBranch: function(branch) {
            this.showLoading('Switching branch...');

            $.post(ajaxurl, {
                action: 'git_switch_branch',
                branch: branch,
                nonce: wpGitManager.nonce
            }, function(response) {
                ManagePage.hideLoading();
                if (response.success) {
                    ManagePage.showMessage('Switched to branch: ' + branch, 'success');
                    ManagePage.loadBranches();
                } else {
                    ManagePage.showMessage('Error switching branch: ' + response.data, 'error');
                }
            }).fail(function(xhr, status, error) {
                ManagePage.hideLoading();
                console.error('Switch branch AJAX Error:', xhr.responseText);
                ManagePage.showMessage('AJAX Error: ' + error + ' (Status: ' + xhr.status + ')', 'error');
            });
        }
    };

    // Initialize ManagePage
    ManagePage.init();
});
